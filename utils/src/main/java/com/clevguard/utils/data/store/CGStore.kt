package com.clevguard.utils.data.store

import android.text.TextUtils
import com.tencent.mmkv.MMKV

/**
 * 创建本地持久化保存工具
 * 使用:
 * 创建:  val mStore:CGStore=createStore(context,"storeName")
 * 设置:  mStore.edit().putXXX(key,value)
 * 获取:  mStore.getXXX(key)
 */
fun createStore(storeName: String): CGStore = CGStore(storeName)

class CGStore(name: String) {
    private val mmkv = MMKV.mmkvWithID(name)

    fun getInt(key: String, defaultValue: Int? = null): Int =
        mmkv.getInt(key, defaultValue ?: 0)

    fun getString(key: String, defaultValue: String? = null): String? {
        val value = mmkv.getString(key, defaultValue)
        return if (TextUtils.isEmpty(value)) {
            defaultValue
        } else value
    }

    fun getStringNotNull(key: String, defaultValue: String): String {
        val value = mmkv.getString(key, defaultValue)
        if (value == null) return defaultValue
        return value
    }

    fun getLong(key: String, defaultValue: Long? = null): Long =
        mmkv.getLong(key, defaultValue ?: 0L)

    fun getBoolean(key: String, defaultValue: Boolean? = null): Boolean =
        mmkv.getBoolean(key, defaultValue == true)

    fun getFloat(key: String, defaultValue: Float? = null): Float =
        mmkv.getFloat(key, defaultValue ?: 0F)

    fun clear(): CGStore {
        mmkv.clear()
        return this
    }

    fun putLong(key: String, value: Long): CGStore {
        mmkv.putLong(key, value)
        return this
    }

    fun putInt(key: String, value: Int): CGStore {
        mmkv.putInt(key, value)
        return this
    }

    fun remove(key: String): CGStore {
        mmkv.remove(key)
        return this
    }

    fun putString(key: String, value: String?): CGStore {
        mmkv.putString(key, value)
        return this
    }

    fun putBoolean(key: String, value: Boolean): CGStore {
        mmkv.putBoolean(key, value)
        return this
    }

    fun putFloat(key: String, value: Float): CGStore {
        mmkv.putFloat(key, value)
        return this
    }

    fun getStringSet(key: String, defaultValue: Set<String> = setOf()): Set<String> {
        return mmkv.getStringSet(key, defaultValue) ?: setOf()
    }

    fun putStringSet(key: String, value: Set<String>): CGStore {
        mmkv.putStringSet(key, value)
        return this
    }
}
