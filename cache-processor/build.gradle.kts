plugins {
    id("com.android.library")
    id("kotlin-android")
    id("com.google.devtools.ksp")
}

apply(from = "${rootProject.projectDir}/common.gradle")
apply(from = "${rootProject.projectDir}/channel.gradle")
apply(from = "${rootProject.projectDir}/build_type.gradle")

android {
    namespace = "com.imyfone.cache_processor"
}

dependencies {
    implementation(project(":cache-annotation"))
    implementation(libs.symbol.processing.api)
    implementation(libs.kotlinpoet)
    implementation(libs.kotlinpoet.ksp)
}