package com.imyfone.cache_processor

import com.google.devtools.ksp.processing.Resolver
import com.google.devtools.ksp.processing.SymbolProcessor
import com.google.devtools.ksp.processing.SymbolProcessorEnvironment
import com.google.devtools.ksp.symbol.ClassKind
import com.google.devtools.ksp.symbol.KSAnnotated
import com.google.devtools.ksp.symbol.KSClassDeclaration
import com.google.devtools.ksp.validate
import com.imyfone.cache.CacheKey
import com.imyfone.cache.LocalCache
import com.squareup.kotlinpoet.ClassName
import com.squareup.kotlinpoet.FileSpec
import com.squareup.kotlinpoet.FunSpec
import com.squareup.kotlinpoet.KModifier
import com.squareup.kotlinpoet.MemberName
import com.squareup.kotlinpoet.PropertySpec
import com.squareup.kotlinpoet.TypeSpec
import com.squareup.kotlinpoet.ksp.toClassName
import com.squareup.kotlinpoet.ksp.writeTo

class LocalCacheSymbolProcessor(environment: SymbolProcessorEnvironment) : SymbolProcessor {
    private val codeGenerator = environment.codeGenerator
    private val logger = environment.logger

    override fun process(resolver: Resolver): List<KSAnnotated> {
        val localCacheAnnotationName = LocalCache::class.qualifiedName!!

        val symbols = resolver.getSymbolsWithAnnotation(localCacheAnnotationName)
        val ret = symbols.filter { !it.validate() }.toList()

        symbols
            .filterIsInstance<KSClassDeclaration>()
            .filter { it.validate() }
            .forEach { interfaceDeclaration ->
                // 1. 验证注解的正确性
                if (interfaceDeclaration.classKind != ClassKind.INTERFACE) {
                    logger.error(
                        "Only interfaces can be annotated with @LocalCache",
                        interfaceDeclaration
                    )
                    return@forEach
                }

                // 2. 获取 @LocalCache 注解的值
                val storeName = interfaceDeclaration.annotations
                    .find { it.shortName.asString() == LocalCache::class.simpleName }
                    ?.arguments
                    ?.find { it.name?.asString() == "storeName" }
                    ?.value as? String ?: ""

                // 3. 构建实现类
                val interfaceName = interfaceDeclaration.toClassName()
                val packageName = interfaceDeclaration.packageName.asString()
                val implementationClassName = "${interfaceName.simpleName}Impl"

                // 使用 KotlinPoet 创建单例对象
                val objectBuilder = TypeSpec.objectBuilder(implementationClassName)
                    .addSuperinterface(interfaceName)

                // 添加 userInfoStore 属性
                val cgStoreClass = ClassName("com.clevguard.utils.data.store", "CGStore")
                val createStoreFun = MemberName("com.clevguard.utils.data.store", "createStore")
                val userInfoStoreProperty = PropertySpec.builder("userInfoStore", cgStoreClass)
                    .delegate("lazy { %M(%S) }", createStoreFun, storeName)
                    .addModifiers(KModifier.PRIVATE)
                    .build()

                objectBuilder.addProperty(userInfoStoreProperty)

                // 4. 遍历属性并生成 Getter/Setter
                interfaceDeclaration.getAllProperties()
                    .forEach { property ->
                        val cacheKeyAnnotation = property.annotations
                            .find { it.shortName.asString() == CacheKey::class.simpleName }

                        if (cacheKeyAnnotation != null) {
                            val key = cacheKeyAnnotation.arguments
                                .find { it.name?.asString() == "key" }
                                ?.value as? String ?: ""
                            val propertyName = property.simpleName.asString()
                            val propertyType = property.type.resolve().toClassName()
                            val propertyTypeName = propertyType.simpleName

                            // 创建属性的 Getter 和 Setter
                            val propertySpec = PropertySpec.builder(propertyName, propertyType)
                                .addModifiers(KModifier.OVERRIDE)
                                .mutable(true)
                                .getter(
                                    FunSpec.getterBuilder()
                                        .addCode(
                                            """
                                            |return when (%S) {
                                            |    "Int" -> userInfoStore.getInt(%S, 0)
                                            |    "String" -> userInfoStore.getString(%S, null) ?: ""
                                            |    "Boolean" -> userInfoStore.getBoolean(%S, false)
                                            |    "Long" -> userInfoStore.getLong(%S, 0L)
                                            |    else -> throw IllegalArgumentException("Unsupported type for property %S: ${'$'}{property.type.resolve().declaration.qualifiedName?.asString()}")
                                            |} as %T
                                            """.trimMargin(),
                                            propertyTypeName,
                                            key,
                                            key,
                                            key,
                                            key,
                                            propertyName,
                                            propertyType
                                        )
                                        .build()
                                )
                                .setter(
                                    FunSpec.setterBuilder()
                                        .addParameter("value", propertyType)
                                        .addCode(
                                            """
                                            |return when (%S) {
                                            |    "Int" -> userInfoStore.putInt(%S, value as Int)
                                            |    "String" -> userInfoStore.putString(%S, value as String)
                                            |    "Boolean" -> userInfoStore.putBoolean(%S, value as Boolean)
                                            |    "Long" -> userInfoStore.putLong(%S, value as Long)
                                            |    else -> throw IllegalArgumentException("Unsupported type for property %S: ${'$'}{property.type.resolve().declaration.qualifiedName?.asString()}")
                                            |}
                                            """.trimMargin(),
                                            propertyTypeName,
                                            key,
                                            key,
                                            key,
                                            key,
                                            propertyName
                                        )
                                        .build()
                                )
                                .build()

                            objectBuilder.addProperty(propertySpec)
                        }
                    }

                // 5. 创建文件并写入
                FileSpec.builder(packageName, implementationClassName)
                    .addType(objectBuilder.build())
                    .addImport("com.clevguard.utils.data.store", "CGStore", "createStore")
                    .build()
                    .writeTo(codeGenerator, false)
            }
        return ret
    }
}