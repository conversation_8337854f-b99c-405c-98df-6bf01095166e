package com.deeptalkie.main

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.core.net.toUri
import com.clevguard.utils.ext.loge
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.activity.WebActivity
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.ui.page.main.mine.SvUserInfoViewModel
import com.deeptalkie.main.config.Constant
import com.deeptalkie.main.config.Constant.INFORMATION_SOURCES
import com.deeptalkie.main.ext.stateInApp
import com.deeptalkie.main.repo.GuestRepo
import com.imyfone.membership.MembershipClient
import com.imyfone.membership.api.bean.MemberBean
import com.imyfone.membership.api.bean.UserBean
import com.imyfone.membership.repository.AccountCommonCode
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.map
import okhttp3.HttpUrl.Companion.toHttpUrl

/**
 *creater:linjinhao on 2025/5/13 10:04
 */
@SuppressLint("StaticFieldLeak")
object Membership {
    private var completableDeferred: CompletableDeferred<String>? = null
    lateinit var membershipClient: MembershipClient
        private set

    val account get() = membershipClient.account

    private lateinit var _userStateFlow: StateFlow<UserBean?>
    val userStateFlow get() = _userStateFlow

    private lateinit var _memberStateFlow: StateFlow<MemberBean?>
    val memberStateFlow get() = _memberStateFlow

    private lateinit var _vipStateFlow: StateFlow<Boolean>
    val vipStateFlow get() = _vipStateFlow

    val userIdFlow: Flow<String?> get() = userStateFlow.map { it?.memberID }

    /**
     * 登录失效的事件
     */
    private val _loginInvalidEvent = MutableSharedFlow<LoginInvalidEvent>()
    val loginInvalidEvent = _loginInvalidEvent.asSharedFlow()

    /**
     * 登录成功的事件
     */
    private val _loginSuccessEvent = MutableSharedFlow<ReportUserInfoEvent>()
    val loginSuccessEvent = _loginSuccessEvent.asSharedFlow()

    fun loginSuccess() {
        logv("登录成功")
        SvUserInfoViewModel.resetClickLogout()
        App.launch {
            _loginSuccessEvent.emit(ReportUserInfoEvent.LoginSuccess)
        }
    }

    fun initClient() {
        membershipClient = MembershipClient.Builder(App.getInstance())
            .pid(INFORMATION_SOURCES)
            .accountBaseURL(BuildConfig.BaseUrl).transferGoogleSkuID { "" }
            .getDeviceCode { UserManager.getDeviceId() }
            .iCartBrowser { s, completableDeferred ->
                makePurchasePending(App.getInstance(), s, completableDeferred)
            }.build()

        _userStateFlow = account.userFlow.stateInApp(SharingStarted.Eagerly)
        _memberStateFlow = account.memberFlow.stateInApp(SharingStarted.Eagerly)
        _vipStateFlow =
            UserManager.vipState.stateInApp(initialValue = false, SharingStarted.Eagerly)

        membershipClient.account.onLoginInvalid = {
            clearVipStatInfo()
            when (it) {
                AccountCommonCode.LOGIN_INVALID -> {
                    if (!SvUserInfoViewModel.clickLogout) {
                        App.launch {
                            _loginInvalidEvent.emit(LoginInvalidEvent.LoginInvalid)
                        }
                    }
                }

                AccountCommonCode.LOGIN_OUT_BY_OTHER -> {
                    App.launch {
                        _loginInvalidEvent.emit(LoginInvalidEvent.LoginOutByOther)
                    }
                }

                AccountCommonCode.LOGIN_OUT_BY_CHANGE_PASSWORD -> {
                    App.launch {
                        _loginInvalidEvent.emit(LoginInvalidEvent.LoginOutByChangePassword)
                    }
                }
            }
        }
    }

    /**
     * 挂起购买，等待网页结果返回
     */
    private fun makePurchasePending(
        context: Context,
        url: String,
        completableDeferred: CompletableDeferred<String>
    ) {
        this.completableDeferred?.cancel()
        this.completableDeferred = completableDeferred
        // 字符串拼接
        val purchaseURL = try {
            url.toHttpUrl().newBuilder()
                .addEncodedQueryParameter(
                    "pid",
                    INFORMATION_SOURCES
                )
                .addEncodedQueryParameter("custom", Constant.fromSite)
                .addEncodedQueryParameter("tpcp", BuildConfig.channel)
                .build()
                .toString()
        } catch (_: Exception) {
            url
        }
        WebActivity.purchase(context, purchaseURL)
    }

    internal fun completeICartPurchase(email: String) {
        this.completableDeferred?.complete(email)
        this.completableDeferred = null
    }

    fun toGoogleSubscription(context: Context) {
        try {
            val url = "https://play.google.com/store/account/subscriptions"
            val intent = Intent(Intent.ACTION_VIEW).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                data = url.toUri()
                setPackage("com.android.vending")
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private val guestRepo = GuestRepo()

    fun guestLogin() {
        if (isLogin()) return
        App.launch {
            guestRepo.loginByGuest()
        }
    }

    fun getUserBean() = userStateFlow.value

    fun getUserId() = getUserBean()?.memberID

    fun getEmail() = getUserBean()?.email

    fun isLogin() = getUserBean()?.memberID != null

    fun isVip() = vipStateFlow.value

    fun logout() {
        if (SvUserInfoViewModel.clickLogout) return
        loge("用户token过期，退出登录")
        App.launch {
            _loginInvalidEvent.emit(LoginInvalidEvent.LoginInvalid)
        }
    }

    val token get() = getUserBean()?.token.orEmpty()

    fun clearVipStatInfo() {
        UserManager.setUserCoins(0)
        UserManager.updateVipState(false)
        UserManager.updateVipFailureTimeText("")
    }
}

sealed interface LoginInvalidEvent {
    data object LoginInvalid : LoginInvalidEvent
    data object LoginOutByOther : LoginInvalidEvent
    data object LoginOutByChangePassword : LoginInvalidEvent
}

sealed interface ReportUserInfoEvent {
    data object LoginSuccess : ReportUserInfoEvent
}