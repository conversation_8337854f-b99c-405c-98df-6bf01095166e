package com.deeptalkie.main.bean

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.provider.Settings
import com.clevguard.utils.data.store.CGStore
import com.clevguard.utils.data.store.createStore
import com.clevguard.utils.utils.newTimeFMTStr
import com.deeptalkie.main.App
import com.deeptalkie.main.Membership
import com.deeptalkie.main.compose.ui.page.main.language.AppSupportedLanguage
import com.deeptalkie.main.config.ReportEventUtils
import com.deeptalkie.main.config.UmConstant
import com.deeptalkie.main.ext.stateInAppDefault
import com.deeptalkie.main.proto.appDataStore
import com.deeptalkie.main.proto.updateAppSettings
import com.deeptalkie.main.proto.updateVipInfo
import com.deeptalkie.main.utils.TimeUtils.YMD2
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import java.util.Random
import java.util.UUID

/**
 *
 * @Description: java类作用描述
 * @Author: LeeQiuuu
 * @CreateDate: 2022/7/28 11:41
 */
object UserManager {
    private const val USER_INFO = "UserInfo"
    private val datastore = App.getInstance().appDataStore

    private val userInfoStore: CGStore by lazy {
        createStore(USER_INFO)
    }

    /**
     * 1.1.0 start
     */
    //只有购买了订阅类型的才是vip，购买了金币的则是普通用户
    val vipState = datastore.data.map { it.vipInfo.vipState }.distinctUntilChanged()

    fun updateVipState(value: Boolean) {
        App.launch {
            datastore.updateData {
                it.updateVipInfo {
                    this.setVipState(value)
                }
            }
        }
    }

    val userCoinsFlow = datastore.data.map { it.vipInfo.coins }.distinctUntilChanged()
    fun setUserCoins(value: Int?) {
        App.launch {
            datastore.updateData {
                it.updateVipInfo {
                    if (value == null) {
                        this.setCoins(0)
                    } else {
                        this.setCoins(value)
                    }
                }
            }
        }
    }

    val vipFailureTimeTextFlow =
        datastore.data.map { it.vipInfo.vipFailureTimeText }.distinctUntilChanged()

    fun updateVipFailureTimeText(value: String) {
        App.launch {
            datastore.updateData {
                it.updateVipInfo {
                    this.setVipFailureTimeText(value)
                }
            }
        }
    }

    val languageFlow = datastore.data.map {
        val languageId = it.appSettings.language
        AppSupportedLanguage.entries.find { lang -> lang.id == languageId }
            ?: AppSupportedLanguage.ENGLISH
    }.stateInAppDefault(AppSupportedLanguage.ENGLISH)

    /**
     * 注销账号发送验证码的时间
     */
    val sendVerificationCodeTime =
        datastore.data.map { it.appSettings.sendVerificationCodeTime }.distinctUntilChanged()

    fun setSendVerificationCodeTime(value: Long) {
        App.launch {
            datastore.updateData {
                it.updateAppSettings {
                    setSendVerificationCodeTime(value)
                }
            }
        }
    }

    fun isFirstTimeLaunchApp(): Boolean {
        return userInfoStore.getBoolean("is_first_time_launch_app", true)
    }

    fun setIsFirstTimeLaunchAppFalse() {
        userInfoStore.putBoolean("is_first_time_launch_app", false)
    }

    private fun aiReplyCountKey(userId: String) = "${userId}_AI_REPLY_COUNT"

    fun getAIReplyLastCount(): Int {
        if (Membership.isVip()) return Int.MAX_VALUE
        return 3 - getAIReplyCount()
    }

    private fun getAIReplyCount(): Int {
        val userId = Membership.getUserId() ?: return 3
        return userInfoStore.getInt(aiReplyCountKey(userId), 0)
    }

    fun plusAIReplyCount() {
        val userId = Membership.getUserId() ?: return
        userInfoStore.putInt(aiReplyCountKey(userId), getAIReplyCount() + 1)
    }

    fun isFromNet(): Boolean {
        return "14" == (userInfoStore.getString("source_id", "1") ?: "")
    }

    fun isAgreePrivacyPolicy(): Boolean =
        userInfoStore.getBoolean("KEY_HAS_AGREE_PRIVACY_POLICY", false)

    fun setAgreePrivacyPolicy() {
        userInfoStore.putBoolean("KEY_HAS_AGREE_PRIVACY_POLICY", true)
    }

    const val LANGUAGE_ID = "languageId"
    fun setLanguageId(id: String) {
        userInfoStore.putString(LANGUAGE_ID, id)
        App.launch {
            datastore.updateData {
                it.updateAppSettings {
                    setLanguage(id)
                }
            }
        }
    }

    fun getLanguageId(): String? {
        return userInfoStore.getString(LANGUAGE_ID)
    }

    fun getLanguage(): AppSupportedLanguage {
        return AppSupportedLanguage.entries.find { it.id == getLanguageId() }
            ?: AppSupportedLanguage.ENGLISH
    }

    fun setTtsTrialCount(count: Int) {
        val ttsTrialKey = "ttsTrialKey_${Membership.getEmail() ?: ""}"
        userInfoStore.putInt(ttsTrialKey, count)
    }

    fun getTtsTrialCount(): Int {
        val ttsTrialKey = "ttsTrialKey_${Membership.getEmail() ?: ""}"
        return userInfoStore.getInt(ttsTrialKey)
    }

    private const val AGE_KEY = "ageKey"

    fun setAge(age: Int) {
        userInfoStore.putInt(AGE_KEY, age)
    }

    fun getAge(): Int {
        return userInfoStore.getInt(AGE_KEY)
    }

    private const val GENDER_KEY = "genderKey"

    fun setGender(gender: Int) {
        userInfoStore.putInt(GENDER_KEY, gender)
    }

    fun getGender(): Int {
        return userInfoStore.getInt(GENDER_KEY)
    }

    fun hasReportUserInfo(): Boolean {
        return userInfoStore.getBoolean("submitUserInfo", false)
    }

    fun setHasReportUserInfo() {
        userInfoStore.putBoolean("submitUserInfo", true)
    }

    private fun createAIRoleKey(userId: String) =
        "${userId}_CREATE_AI_ROLE_KEY_${newTimeFMTStr(format = YMD2)}"

    fun canCreateAIRole(): Boolean {
        if (Membership.isVip()) return true
        return getCreateAIRoleCount() < 3
    }

    private fun getCreateAIRoleCount(): Int {
        val userId = Membership.getUserId() ?: return 3
        return userInfoStore.getInt(createAIRoleKey(userId), 0)
    }

    fun plusCreateAIRoleCount() {
        if (Membership.isVip()) return
        val userId = Membership.getUserId() ?: return
        userInfoStore.putInt(createAIRoleKey(userId), getCreateAIRoleCount() + 1)
    }

    fun getToken(): String {
        return userInfoStore.getString("token", "") ?: ""
    }

    fun initLanguage(context: Context) {
        // 没设置过语言，就尝试使用手机的语言
        if (getLanguageId() != null) return
        val phoneLanguage = context.resources.configuration.locales.get(0)
        val language = AppSupportedLanguage.entries.find { it.locale == phoneLanguage }
            ?: AppSupportedLanguage.ENGLISH
        setLanguageId(language.id)
    }

    private const val LAST_ENTER_DATE_KEY = "LAST_ENTER_DATE_KEY"

    fun onAppEnterEvent() {
        val date = newTimeFMTStr(format = YMD2)
        val lastEnterDate = userInfoStore.getString(LAST_ENTER_DATE_KEY, "")
        if (date == lastEnterDate) {
            return
        }
        userInfoStore.putString(LAST_ENTER_DATE_KEY, date)

        ReportEventUtils.onEvent(
            UmConstant.CHAT_DATA,
            mapOf(UmConstant.CHAT_DATA to "Users_Startup")
        )
    }

    private const val LAST_ENTER_CHAT_DATE_KEY = "LAST_ENTER_CHAT_DATE_KEY"

    fun onChatEnterEvent() {
        val date = newTimeFMTStr(format = YMD2)
        val lastEnterDate = userInfoStore.getString(LAST_ENTER_CHAT_DATE_KEY, "")
        if (date == lastEnterDate) {
            return
        }
        userInfoStore.putString(LAST_ENTER_CHAT_DATE_KEY, date)

        ReportEventUtils.onEvent(
            UmConstant.CHAT_DATA,
            mapOf(UmConstant.CHAT_DATA to "Users_First_interview")
        )
    }

    private const val LAST_SEND_MSG_DATE_KEY = "LAST_SEND_MSG_DATE_KEY"

    fun onSendMsgEvent() {
        val date = newTimeFMTStr(format = YMD2)
        val lastSendMsgDate = userInfoStore.getString(LAST_SEND_MSG_DATE_KEY, "")
        if (date == lastSendMsgDate) {
            return
        }
        userInfoStore.putString(LAST_SEND_MSG_DATE_KEY, date)

        ReportEventUtils.onEvent(
            UmConstant.CHAT_DATA,
            mapOf(UmConstant.CHAT_DATA to "Users_First_message")
        )
    }

    private const val DEVICE_ID_KEY = "DEVICE_ID_KEY"

    fun getDeviceId(): String {
        val deviceId = userInfoStore.getString(DEVICE_ID_KEY, null)
        if (deviceId == null) {
            val newDeviceId = newDeviceId()
            setDeviceId(newDeviceId)
            return newDeviceId
        }
        return deviceId
    }

    fun setDeviceId(deviceId: String) {
        userInfoStore.putString(DEVICE_ID_KEY, deviceId)
    }

    @SuppressLint("HardwareIds")
    private fun newDeviceId(): String {
        val androidId =
            Settings.Secure.getString(App.getInstance().contentResolver, Settings.Secure.ANDROID_ID)
        if (androidId == null) {
            return getNativeDeviceId(createNonceStr())
        }
        return androidId
    }

    private fun getNativeDeviceId(salt: String): String {
        val mDeviceIdShort = StringBuilder()
            .append("FamiGuardCN_Parent")
            .append(Build.BOARD)
            .append(Build.CPU_ABI)
            .append(Build.DEVICE)
            .append(Build.MANUFACTURER)
            .append(Build.MODEL)
            .append(Build.PRODUCT).toString()
        var serial: String?
        try {
            serial = Build::class.java.getField("SERIAL").get(null)?.toString()
            if ("unknown" == serial) {
                serial = salt
            }
            return UUID(mDeviceIdShort.hashCode().toLong(), serial.hashCode().toLong()).toString()
        } catch (_: Exception) {
            serial = salt
        }
        return UUID(mDeviceIdShort.hashCode().toLong(), serial.hashCode().toLong()).toString()
    }

    private fun createNonceStr(): String {
        val builder = StringBuilder()
        val randomStr = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        val count = randomStr.length - 1
        val random = Random()
        (0..7).forEach { i ->
            val id = random.nextInt(count)
            val s = randomStr.substring(id, id + 1)
            builder.append(s)
        }
        return builder.toString()
    }

    private const val GUEST_CODE_KEY = "GUEST_CODE_KEY"

    fun getGuestCode(): String? {
        return userInfoStore.getString(GUEST_CODE_KEY, null)
    }

    fun setGuestCode(guestCode: String) {
        userInfoStore.putString(GUEST_CODE_KEY, guestCode)
    }

    private const val GUEST_EMAIL_KEY = "GUEST_EMAIL_KEY"

    fun getGuestEmail(): String? {
        return userInfoStore.getString(GUEST_EMAIL_KEY, null)
    }

    fun setGuestEmail(guestEmail: String) {
        userInfoStore.putString(GUEST_EMAIL_KEY, guestEmail)
    }
}