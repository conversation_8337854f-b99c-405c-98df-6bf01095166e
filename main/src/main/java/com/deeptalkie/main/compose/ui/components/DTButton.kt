package com.deeptalkie.main.compose.ui.components

import androidx.annotation.StringRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.takeOrElse
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.compose.theme.Transparent
import kotlinx.coroutines.delay

@Composable
fun DTButton(
    text: String,
    brush: Brush,
    modifier: Modifier = Modifier,
    enable: Boolean = true,
    subText: String = "",
    shape: Shape = RoundedCornerShape(50),
    elevation: Dp = 0.dp,
    textStyle: TextStyle = DTButtonTextStyle,
    onClick: () -> Unit,
) {
    Button(
        onClick, modifier, enable,
        colors = ButtonDefaults.buttonColors(
            containerColor = Transparent,
            contentColor = MaterialTheme.colorScheme.onPrimary,
            disabledContainerColor = Transparent,
            disabledContentColor = MaterialTheme.colorScheme.onPrimary,
        ),
        shape = shape,
        elevation = ButtonDefaults.elevatedButtonElevation(
            elevation, elevation, elevation, elevation,
        ),
        contentPadding = PaddingValues(0.dp)
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .background(brush)
                .padding(DTButtonDefaults.ContentPadding),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            AutoSizeText(
                text = text,
                style = textStyle,
                maxLines = 1,
                autoSize = DTButtonDefaults.autoSize(
                    maxFontSize = textStyle.fontSize
                ),
            )
            if (!subText.isEmpty()) {
                Text(
                    text = subText,
                    color = MaterialTheme.colorScheme.onSecondary,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W400,
                )
            }
        }
    }
}

@Composable
fun DTButton(
    text: String,
    bg: SelectableDrawable,
    modifier: Modifier = Modifier,
    enable: Boolean = true,
    subText: String = "",
    contentPadding: PaddingValues = DTButtonDefaults.ContentPadding,
    shape: Shape = RoundedCornerShape(50),
    elevation: Dp = 0.dp,
    textStyle: TextStyle = DTButtonTextStyle,
    onClick: () -> Unit,
) {
    Button(
        onClick, modifier, enable,
        colors = ButtonDefaults.buttonColors(
            containerColor = Transparent,
            contentColor = MaterialTheme.colorScheme.onPrimary,
            disabledContainerColor = Transparent,
            disabledContentColor = MaterialTheme.colorScheme.onPrimary,
        ),
        shape = shape,
        elevation = ButtonDefaults.elevatedButtonElevation(
            elevation, elevation, elevation, elevation,
        ),
        contentPadding = contentPadding
    ) {
        val bgImage = if (enable) bg.enableDrawable else bg.disableDrawable
        Box(Modifier.fillMaxSize()) {
            Image(
                painterResource(bgImage),
                null,
                Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )
            Column(
                Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AutoSizeText(
                    text = text,
                    style = textStyle,
                    maxLines = 1,
                    autoSize = DTButtonDefaults.autoSize(
                        maxFontSize = textStyle.fontSize
                    ),
                )
                if (!subText.isEmpty()) {
                    Text(
                        text = subText,
                        color = MaterialTheme.colorScheme.onSecondary,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W400,
                    )
                }
            }
        }
    }
}

@Composable
fun DTButton(
    text: String,
    bg: SelectableDrawable,
    contentColor: Color = MaterialTheme.colorScheme.onPrimary,
    disabledContentColor: Color = MaterialTheme.colorScheme.onPrimary,
    modifier: Modifier = Modifier,
    enable: Boolean = true,
    subText: String = "",
    contentPadding: PaddingValues = DTButtonDefaults.ContentPadding,
    shape: Shape = RoundedCornerShape(50),
    elevation: Dp = 0.dp,
    textStyle: TextStyle = DTButtonTextStyle,
    onClick: () -> Unit,
) {
    Button(
        onClick, modifier, enable,
        colors = ButtonDefaults.buttonColors(
            containerColor = Transparent,
            contentColor = contentColor,
            disabledContainerColor = Transparent,
            disabledContentColor = disabledContentColor,
        ),
        shape = shape,
        elevation = ButtonDefaults.elevatedButtonElevation(
            elevation, elevation, elevation, elevation,
        ),
        contentPadding = contentPadding
    ) {
        val bgImage = if (enable) bg.enableDrawable else bg.disableDrawable
        Box(Modifier.fillMaxSize()) {
            Image(
                painterResource(bgImage),
                null,
                Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )
            Column(
                Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AutoSizeText(
                    text = text,
                    style = textStyle,
                    maxLines = 1,
                    autoSize = DTButtonDefaults.autoSize(
                        maxFontSize = textStyle.fontSize
                    ),
                )
                if (!subText.isEmpty()) {
                    Text(
                        text = subText,
                        color = MaterialTheme.colorScheme.onSecondary,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W400,
                    )
                }
            }
        }
    }
}

@Composable
fun DTButton(
    text: String,
    modifier: Modifier = Modifier,
    enable: Boolean = true,
    background: Color = Transparent,
    border: BorderStroke? = null,
    shape: Shape = RoundedCornerShape(50),
    elevation: Dp = 0.dp,
    textStyle: TextStyle = DTButtonTextStyle,
    contentPadding: PaddingValues = DTButtonDefaults.ContentPadding,
    onClick: () -> Unit,
) {
    Button(
        onClick, modifier, enable,
        colors = ButtonDefaults.buttonColors(
            containerColor = background,
            contentColor = MaterialTheme.colorScheme.onPrimary,
            disabledContainerColor = Transparent,
            disabledContentColor = MaterialTheme.colorScheme.onPrimary,
        ),
        shape = shape,
        elevation = ButtonDefaults.elevatedButtonElevation(
            elevation, elevation, elevation, elevation,
        ),
        border = border,
        contentPadding = contentPadding
    ) {
        AutoSizeText(
            text = text,
            style = textStyle,
            maxLines = 1,
            autoSize = DTButtonDefaults.autoSize(
                maxFontSize = textStyle.fontSize
            ),
        )
    }
}

@Composable
fun DTButton(
    @StringRes text: Int,
    modifier: Modifier = Modifier,
    enable: Boolean = true,
    containerColor: Color = Transparent,
    disabledContainerColor: Color = Transparent,
    contentColor: Color = MaterialTheme.colorScheme.onPrimary,
    disabledContentColor: Color = MaterialTheme.colorScheme.onPrimary,
    border: BorderStroke? = null,
    shape: Shape = RoundedCornerShape(50),
    elevation: Dp = 0.dp,
    textStyle: TextStyle = DTButtonTextStyle,
    onClick: () -> Unit,
) {
    Button(
        onClick, modifier, enable,
        colors = ButtonDefaults.buttonColors(
            containerColor = containerColor,
            contentColor = contentColor,
            disabledContainerColor = disabledContainerColor,
            disabledContentColor = disabledContentColor,
        ),
        shape = shape,
        elevation = ButtonDefaults.elevatedButtonElevation(
            elevation, elevation, elevation, elevation,
        ),
        border = border,
        contentPadding = DTButtonDefaults.ContentPadding
    ) {
        AutoSizeText(
            text = stringResource(text),
            style = textStyle,
            maxLines = 1,
            autoSize = DTButtonDefaults.autoSize(
                maxFontSize = textStyle.fontSize
            ),
        )
    }
}

@Composable
fun VerifyCodeButton(
    lastGetCodeTime: Long,
    modifier: Modifier = Modifier,
    buttonText: String,
    enable: Boolean = true,
    containerColor: Color = Transparent,
    disabledContainerColor: Color = Transparent,
    contentColor: Color = MaterialTheme.colorScheme.onPrimary,
    disabledContentColor: Color = MaterialTheme.colorScheme.onPrimary,
    border: BorderStroke? = null,
    shape: Shape = RoundedCornerShape(50),
    elevation: Dp = 0.dp,
    textStyle: TextStyle = DTButtonTextStyle,
    onClick: () -> Unit,
) {
    val enable by rememberUpdatedState(newValue = enable)
    var actionDown by remember { mutableStateOf(false) }
    val vail by remember { derivedStateOf { enable && !actionDown } }
    Button(
        onClick, modifier, vail,
        colors = ButtonDefaults.buttonColors(
            containerColor = containerColor,
            contentColor = contentColor,
            disabledContainerColor = disabledContainerColor,
            disabledContentColor = disabledContentColor,
        ),
        shape = shape,
        elevation = ButtonDefaults.elevatedButtonElevation(
            elevation, elevation, elevation, elevation,
        ),
        border = border,
        contentPadding = DTButtonDefaults.ContentPadding
    ) {
        var text by remember { mutableStateOf("") }

        AutoSizeText(
            text = text,
            style = textStyle,
            maxLines = 1,
            autoSize = DTButtonDefaults.autoSize(
                maxFontSize = textStyle.fontSize
            ),
        )
        LaunchedEffect(lastGetCodeTime) {
            var now: Long

            // 倒计时中
            while (System.currentTimeMillis().also { now = it } - lastGetCodeTime < 60_000) {
                val delta = 60 - (now - lastGetCodeTime) / 1000
                text = "${delta}s"
                actionDown = true
                delay(1000)
            }
            actionDown = false
            // 倒计时完成
            text = buttonText
        }
    }
}

@Composable
fun AutoSizeText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    onTextLayout: ((TextLayoutResult) -> Unit)? = null,
    style: TextStyle = LocalTextStyle.current,
    autoSize: TextAutoSize? = null
) {

    val textColor = color.takeOrElse { style.color.takeOrElse { LocalContentColor.current } }

    BasicText(
        text,
        modifier,
        style.merge(
            color = textColor,
            fontSize = fontSize,
            fontWeight = fontWeight,
            textAlign = textAlign ?: TextAlign.Unspecified,
            lineHeight = lineHeight,
            fontFamily = fontFamily,
            textDecoration = textDecoration,
            fontStyle = fontStyle,
            letterSpacing = letterSpacing
        ),
        onTextLayout,
        overflow,
        softWrap,
        maxLines,
        minLines,
        autoSize = autoSize
    )
}