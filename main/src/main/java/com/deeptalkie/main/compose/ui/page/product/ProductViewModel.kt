package com.deeptalkie.main.compose.ui.page.product

import androidx.annotation.StringRes
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.deeptalkie.main.BuildConfig
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.config.ChannelType
import com.deeptalkie.main.config.Constant
import com.deeptalkie.main.config.ReportEventUtils
import com.deeptalkie.main.config.UmConstant
import com.deeptalkie.main.ext.stateInViewModel
import com.deeptalkie.main.repo.GuestRepo
import com.deeptalkie.main.repo.PermissionRepo
import com.deeptalkie.main.repo.UserCoinRepo
import com.deeptalkie.main.utils.getString
import com.imyfone.membership.api.bean.ConfirmResultBean
import com.imyfone.membership.api.bean.SKUBean
import com.imyfone.membership.api.bean.UserBean
import com.imyfone.membership.exception.NeedLoginException
import com.imyfone.membership.exception.UserCancelException
import com.imyfone.membership.repository.ConfirmGooglePurchaseResult
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 *creater:linjinhao on 2025/5/19 16:55
 */
class ProductViewModel : ViewModel() {
    private val userCoinRepo = UserCoinRepo()
    private val permissionRepo = PermissionRepo()
    private val guestRepo = GuestRepo()
    private val account = Membership.membershipClient.account
    private val iCart = Membership.membershipClient.iCart
    private val _state = MutableStateFlow(ProductUIState())
    val state = _state.asStateFlow()
    val benefits = mutableStateListOf<BenefitCompareBean>().apply {
        addAll(getBenefitCompareList())
    }
    var userBean: UserBean? = null
    val isGoogleChannel by mutableStateOf(BuildConfig.channel == ChannelType.CHANNEL_GOOGLE)
    var selectIndex by mutableIntStateOf(0)
    val flow = snapshotFlow { selectIndex }
    val licenseSKu = mutableStateListOf<SKUBean>()
    val coinSKu = mutableStateListOf<SKUBean>()

    val skus = mutableStateListOf<SKUBean>()

    private val _event = MutableSharedFlow<ProductEvent>()
    val event = _event.asSharedFlow()

    // var showTipDialog by mutableStateOf(UserManager.hasShowProductTipDialog())

    var isSelectSubPlan by mutableStateOf(true)
    val vipStateFlow = UserManager.vipState
    var isVip by mutableStateOf(false)
    var showGuestPaySuccessDialog by mutableStateOf(false)
    var payEmail = ""
    var payEmailPassword = ""
    var paySku: SKUBean? = null
    val vipFailureTimeTextFlow = UserManager.vipFailureTimeTextFlow.stateInViewModel()

    init {
        getProduct()
        collectUser()
        reportEvent()
        collectVipState()
    }

    private fun collectVipState() {
        viewModelScope.launch {
            vipStateFlow.collect {
                isVip = it
            }
        }
    }

    fun switchLicenseSku() {
        if (isSelectSubPlan) return
        selectIndex = 0
        isSelectSubPlan = true
        setSku()
    }

    fun switchCoinSku() {
        if (!isSelectSubPlan) return
        selectIndex = 0
        isSelectSubPlan = false
        setSku()
    }

    fun setSku() {
        skus.clear()
        if (isSelectSubPlan) {
            skus.addAll(licenseSKu)
        } else {
            skus.addAll(coinSKu)
        }
    }

    private fun collectUser() {
        viewModelScope.launch {
            account.userFlow.collectLatest { user ->
                userBean = user
            }
        }
    }

    fun reportEvent() {
        viewModelScope.launch {
            if (skus.isNotEmpty()) {
                flow.collect {
                    when (skus[it].licenseID ?: "0") {
                        "1" -> {
                            ReportEventUtils.onEvent(
                                UmConstant.BENEFITS_PLAN,
                                mapOf("type" to "1_Month")
                            )
                        }

                        "2" -> {
                            ReportEventUtils.onEvent(
                                UmConstant.BENEFITS_PLAN,
                                mapOf("type" to "1_Year")
                            )
                        }

                        "3" -> {
                            ReportEventUtils.onEvent(
                                UmConstant.BENEFITS_PLAN,
                                mapOf("type" to "Lifetime")
                            )
                        }

                        "4" -> {
                            ReportEventUtils.onEvent(
                                UmConstant.BENEFITS_PLAN,
                                mapOf("type" to "1-Quarter")
                            )
                        }

                        "5" -> {
                            ReportEventUtils.onEvent(
                                UmConstant.BENEFITS_PLAN,
                                mapOf("type" to "1-DAY")
                            )
                        }
                    }

                    when {
                        skus[it].name.contains("100") -> {
                            ReportEventUtils.onEvent(
                                UmConstant.BENEFITS_PLAN,
                                mapOf("type" to "100")
                            )
                        }

                        skus[it].name.contains("315") -> {
                            ReportEventUtils.onEvent(
                                UmConstant.BENEFITS_PLAN,
                                mapOf("type" to "315")
                            )
                        }

                        skus[it].name.contains("550") -> {
                            ReportEventUtils.onEvent(
                                UmConstant.BENEFITS_PLAN,
                                mapOf("type" to "550")
                            )
                        }

                        skus[it].name.contains("1250") -> {
                            ReportEventUtils.onEvent(
                                UmConstant.BENEFITS_PLAN,
                                mapOf("type" to "1250")
                            )
                        }
                    }
                }
            }
        }
    }

    private fun getProduct() {
        viewModelScope.launch {
            _state.update { it.copy(skuState = SkuUIState.Loading) }
            val response = account.getSKUList()
            if (response.isSuccess) {
                val data = response.data
                licenseSKu.clear()
                coinSKu.clear()
                data?.filter {
                    if (it.licenseID != "3") {
                        licenseSKu.add(it)
                    } else {
                        coinSKu.add(it)
                    }
                }
                setSku()
                _state.update {
                    it.copy(skuState = SkuUIState.Success(data ?: emptyList()))
                }
            } else {
                _state.update { it.copy(skuState = SkuUIState.Fail) }
            }
        }
    }

    fun purchase(skuBean: SKUBean) {
        viewModelScope.launch {
//            _state.update { it.copy(purchaseLoading = true) }
            // 2. 开始购买
            /*  if (BuildConfig.channel == ChannelType.CHANNEL_GOOGLE) {
                  purchaseGoogle(skuBean)
                  _state.update { it.copy(purchaseLoading = false) }
              } else {*/
            purchaseICart(skuBean)
//            _state.update { it.copy(purchaseLoading = false) }
//            }
        }
    }

    private suspend fun purchaseGoogle(skuBean: SKUBean) {
        if (userBean == null) {
            _event.emit(ProductEvent.NeedLogin)
            return
        }
        try {
            val googlePurchaseBean = iCart.googlePurchase(
                skuBean,
                source = Constant.fromSite
            )
            var confirming = true
            while (confirming) {
                val result = iCart.confirmGooglePurchase(googlePurchaseBean)
                when (result) {
                    ConfirmGooglePurchaseResult.Pending -> {
                    }

                    is ConfirmGooglePurchaseResult.Success -> {
                        confirming = false
                        _event.emit(ProductEvent.GooglePurchaseSuccess(result.confirmResultBean))
                    }

                    ConfirmGooglePurchaseResult.Fail -> {
                        confirming = false
                        _event.emit(ProductEvent.GoogleConfirmOrderFail)
                    }
                }
                delay(1_000)
            }
            refreshUserCoin()
            refreshVipState()
        } catch (cancel: UserCancelException) {
            _event.emit(ProductEvent.CancelGooglePurchase)
        } catch (e: NeedLoginException) {
            _event.emit(ProductEvent.NeedLogin)
        } catch (e: Exception) {
            _event.emit(ProductEvent.GooglePurchaseFail(e))
        }
    }

    private suspend fun purchaseICart(skuBean: SKUBean) {
        try {
            if (userBean == null) {
                guestRepo.loginByGuest()
            }
            val email = iCart.iCartPurchase(skuBean)
            refreshUserCoin()
            refreshVipState()
            if (email.isNotEmpty() && userBean == null) {
                _event.emit(ProductEvent.LoginTip(email))
            }
            if (email.isNotEmpty() && userBean != null) {
                // 如果是游客登录购买
                if (userBean?.email == UserManager.getGuestEmail()) {
                    showGuestPaySuccessDialog = true
                    payEmail = email
                    payEmailPassword =
                        if (email == UserManager.getGuestEmail()) "a123456" else getString(R.string.guest_pay_success_password)
                    paySku = skuBean
                }
                _event.emit(ProductEvent.ICartPurchaseFinish)
            }
        } catch (e: NeedLoginException) {
            _event.emit(ProductEvent.NeedLogin)
        }
    }

    private suspend fun refreshUserCoin() {
        for (i in 0 until 3) {
            if (userCoinRepo.refreshUserCoin().isSuccess) break
        }
    }

    private suspend fun refreshVipState() {
        for (i in 0 until 3) {
            if (permissionRepo.refreshVipState().isSuccess) break
        }
    }

    private fun getBenefitCompareList(): List<BenefitCompareBean> {
        val bean1 = BenefitCompareBean(
            R.string.unlimited_text_messages,
            "10",
            R.string.unlimited
        )
        val bean2 = BenefitCompareBean(
            R.string.get_100_free,
            "—",
            R.drawable.ic_buy_vip_right, isText = false
        )
        val bean3 = BenefitCompareBean(
            R.string.unlimited_voice_messages,
            "3",
            R.string.unlimited
        )
        val bean4 = BenefitCompareBean(
            R.string.create_custom_character,
            "3",
            R.string.unlimited
        )
        val bean5 = BenefitCompareBean(
            R.string.generate_images,
            "—",
            R.drawable.ic_buy_vip_right, isText = false
        )
        val bean6 = BenefitCompareBean(
            R.string.preview_character_video,
            "—",
            R.string.unlimited, enable = false
        )
        val bean7 = BenefitCompareBean(
            R.string.roaming_character_chat_logs,
            getString(R.string._30_day),
            R.string.unlimited, enable = false
        )
        return listOf(bean1, bean2, bean3, bean4, bean5, bean6, bean7)
    }
}

data class ProductUIState(
    val skuState: SkuUIState = SkuUIState.Init,
    val purchaseLoading: Boolean = false
)

sealed class SkuUIState {
    data object Init : SkuUIState()
    data object Loading : SkuUIState()
    data class Success(val skus: List<SKUBean>) : SkuUIState()
    data object Fail : SkuUIState()
}

data class BenefitCompareBean(
    @StringRes val benefit: Int,
    val premium: String,
    val free: Int,
    val isText: Boolean = true,
    val enable: Boolean = true
)


sealed interface ProductEvent {
    class GooglePurchaseSuccess(val bean: ConfirmResultBean?) : ProductEvent

    class LoginTip(val email: String) : ProductEvent

    data object NeedLogin : ProductEvent

    data object CancelGooglePurchase : ProductEvent

    class GooglePurchaseFail(val e: Exception) : ProductEvent
    data object GoogleConfirmOrderFail : ProductEvent
    data object ICartPurchaseFinish : ProductEvent
}
