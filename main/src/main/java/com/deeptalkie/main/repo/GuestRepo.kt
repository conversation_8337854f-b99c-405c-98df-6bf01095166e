package com.deeptalkie.main.repo

import com.clevguard.utils.ext.logv
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.kidsguard.net.util.EncryptedUtil
import com.deeptalkie.main.Membership
import com.deeptalkie.main.api.accountApi
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.bean.UserManager.getDeviceId
import com.deeptalkie.main.config.Constant
import com.deeptalkie.main.config.Constant.INFORMATION_SOURCES

class GuestRepo : BaseDeepTalkieRepo() {
    private val account get() = Membership.account

    suspend fun loginByGuest() {
        createGuest()
        registerGuestAccount()
    }

    private suspend fun createGuest() {
        val guestCode = UserManager.getGuestCode()
        if (guestCode != null) {
            logv("游客Code已存在:$guestCode")
            return
        }
        runHttp {
            val guestBean = accountApi.reportAndCreateGuest(
                getDeviceId(),
                Constant.language,
                Constant.fromSite,
                INFORMATION_SOURCES
            ).data ?: return@runHttp

            logv("上报游客成功:${guestBean}")
            UserManager.setGuestCode(guestBean.tourists_code)
        }
    }

    private suspend fun registerGuestAccount() {
        val localGuestEmail = UserManager.getGuestEmail()
        if (localGuestEmail != null) {
            guestLogin(localGuestEmail)
            return
        }

        val guestEmail = "deeptalkie${System.currentTimeMillis()}@gmail.com"
        val password = EncryptedUtil.md5("a123456")
        val firstName = "DeepTalkie Guest"

        runHttp {
            val response = account.register(guestEmail, password, firstName, "", Constant.fromSite)
            if (response.isSuccess) {
                logv("注册游客账号成功")
                Membership.loginSuccess()
                UserManager.setGuestEmail(guestEmail)
            } else {
                logv("注册游客账号失败:${response.code}")
            }
        }
    }

    private suspend fun guestLogin(email: String) {
        runHttp {
            val response = account.login(email, EncryptedUtil.md5("a123456"))
            if (response.isSuccess) {
                logv("游客登录成功")
                Membership.loginSuccess()
            } else {
                logv("游客登录失败:${response.code}")
            }
        }
    }
}