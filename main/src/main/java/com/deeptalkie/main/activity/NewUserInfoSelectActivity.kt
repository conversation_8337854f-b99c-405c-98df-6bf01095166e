package com.deeptalkie.main.activity

import android.content.Intent
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.ui.components.AgeSelector
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.components.SelectableDrawable
import com.deeptalkie.main.compose.ui.components.SexSelector

class NewUserInfoSelectActivity : BaseComposeActivity() {
    @Composable
    override fun ComposeContent() {
        var sexIndex by remember { mutableIntStateOf(-1) }
        var ageIndex by remember { mutableIntStateOf(-1) }

        val canSubmit by remember {
            derivedStateOf {
                sexIndex != -1 && ageIndex != -1
            }
        }

        Box(Modifier.fillMaxSize()) {
            val composition by rememberLottieComposition(LottieCompositionSpec.Asset("animations/info_select_page_bg.json"))
            LottieAnimation(
                composition,
                Modifier.fillMaxSize(),
                iterations = LottieConstants.IterateForever
            )
            Column(
                Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    stringResource(R.string.new_user_info_select_page_title),
                    Modifier.fillMaxWidth(),
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.W700,
                    textAlign = TextAlign.Center,
                )
                DTVerticalSpacer(10.dp)
                Text(
                    stringResource(R.string.new_user_info_select_page_content),
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 27.dp),
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Center,
                    lineHeight = 18.sp,
                )
                DTVerticalSpacer(20.dp)
                SexSelector(sexIndex) { index ->
                    sexIndex = index
                }
                DTVerticalSpacer(23.dp)
                AgeSelector(ageIndex) { index ->
                    ageIndex = index
                }
                DTVerticalSpacer(47.dp)
                DTButton(
                    stringResource(R.string.new_user_info_select_page_submit_btn_text),
                    bg = SelectableDrawable(
                        enableDrawable = R.drawable.info_select_page_btn_enable_bg,
                        disableDrawable = R.drawable.btn_disable_bg
                    ),
                    Modifier
                        .padding(horizontal = 20.dp)
                        .fillMaxWidth()
                        .height(45.dp),
                    canSubmit,
                    stringResource(R.string.new_user_info_select_page_submit_btn_sub_text),
                    contentPadding = PaddingValues(0.dp),
                    onClick = {
                        UserManager.setGender(sexIndex + 1)
                        UserManager.setAge(ageIndex + 1)
                        gotoMain()
                    }
                )
                DTVerticalSpacer(54.dp)
            }
        }
    }

    private fun gotoMain() {
        UserManager.setIsFirstTimeLaunchAppFalse()
        startActivity(Intent(this@NewUserInfoSelectActivity, MainActivity::class.java))
        finish()
    }
}