package com.deeptalkie.main.viewmodel

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.clevguard.utils.ext.logv
import com.deeptalkie.kidsguard.net.error
import com.deeptalkie.kidsguard.net.success
import com.deeptalkie.kidsguard.net.ws.WSManager
import com.deeptalkie.kidsguard.net.ws.WsData
import com.deeptalkie.kidsguard.net.ws.WsError
import com.deeptalkie.main.Membership
import com.deeptalkie.main.ReportUserInfoEvent
import com.deeptalkie.main.api.deepTalkieApiFastJson
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.bean.VersionBean
import com.deeptalkie.main.compose.ui.page.chat.MsgRecordRepo
import com.deeptalkie.main.compose.ui.page.main.chats.ChatsRepo
import com.deeptalkie.main.compose.ui.page.main.createrole.CreateAIRoleRepo
import com.deeptalkie.main.compose.utils.AppUtils
import com.deeptalkie.main.config.TimberUtil
import com.deeptalkie.main.repo.AppRepo
import com.deeptalkie.main.repo.PermissionRepo
import com.deeptalkie.main.repo.UserCoinRepo
import kotlinx.coroutines.launch

class MainViewModel : ViewModel() {
    private val userCoin = UserCoinRepo()
    private val permissionRepo = PermissionRepo()
    private val msgRecordRepo = MsgRecordRepo()
    private val appRepo = AppRepo()
    private val createAIRoleRepo = CreateAIRoleRepo()

    var isDownloading by mutableStateOf(false)
    var versionBean by mutableStateOf<VersionBean?>(null)

    init {
        viewModelScope.launch {
            WSManager.websocketMessageFlow.collect { wsResp ->
                when (wsResp) {
                    is WsData -> msgRecordRepo.saveWebSocketMsg(wsResp)
                    is WsError -> {}
                }
            }
        }

        viewModelScope.launch {
            Membership.userStateFlow.collect { userInfo ->
                userInfo ?: return@collect
                if (Membership.isVip()) {
                    val userId = Membership.getUserId() ?: return@collect
                    msgRecordRepo.unlockAllMsg(userId)
                }
            }
        }
        viewModelScope.launch {
            createAIRoleRepo.loadAllTagsToDb()
        }
        handleLoginEvent()
        refreshCoin()
        refreshVipState()
    }

    fun showUpdateDialog(versionBean: VersionBean?) {
        this.versionBean = versionBean
    }

    fun connectWebSocket() {
        viewModelScope.launch {
            Membership.userStateFlow.collect { user ->
                val token = user?.token ?: return@collect
                // 连接 websocket
                WSManager.open(token, UserManager.getLanguage().backendId)
            }
        }
    }

    /**
     * 获取新版本&what's new的信息
     */
    fun getNewVersionInfo(activity: Context, dealShow: (bean: VersionBean) -> Unit) {
        viewModelScope.launch {
            val version = appRepo.getVersionInfo()
            if (version == null || version.ver.isEmpty()) {
                return@launch
            }
            if (version.ver > AppUtils.getVersion(activity)) {
                // 服务器版本比当前版本大，显示更新弹窗
                dealShow(version)
            }
        }
    }

    private fun handleLoginEvent() {
        viewModelScope.launch {
            Membership.loginSuccessEvent.collect {
                when (it) { //登录成功
                    ReportUserInfoEvent.LoginSuccess -> {
                        logv("刷新用户信息")
                        reportUserInfo()
                        prepareUserChatMsg()
                        refreshCoin()
                        refreshVipState()
                    }
                }
            }
        }
    }

    /**
     * 登录成功后上报用户的信息
     */
    fun reportUserInfo() {
        if (!UserManager.hasReportUserInfo()) {
            val member = Membership.memberStateFlow.value
            viewModelScope.launch {
                val name = "${member?.firstName}${member?.lastName}"
                deepTalkieApiFastJson
                    .submitMemberInfo(
                        name = name,
                        email = member?.email ?: "",
                        age = UserManager.getAge(),
                        gender = UserManager.getGender()
                    ).success {
                        UserManager.setHasReportUserInfo()
                    }.error { i, s ->
                        TimberUtil.d("reportUserInfo", "submitMemberInfo code = $i,s = $s")
                    }
            }
        }
    }

    /**
     * 预加载用户聊天
     */
    private fun prepareUserChatMsg() {
        viewModelScope.launch {
            val userId = Membership.getUserId() ?: return@launch
            ChatsRepo().requestSessionList(userId)
        }
    }

    /**
     * 刷新金币
     */
    private fun refreshCoin() {
        viewModelScope.launch {
            if (Membership.isLogin()) {
                repeat(3) {
                    if (userCoin.refreshUserCoin().isSuccess) return@launch
                }
            }
        }
    }

    private fun refreshVipState() {
        viewModelScope.launch {
            if (Membership.isLogin()) {
                repeat(3) {
                    if (permissionRepo.refreshVipState().isSuccess) return@launch
                }
            }
        }
    }
}